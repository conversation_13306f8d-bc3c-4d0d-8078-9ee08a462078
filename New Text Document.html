<!DOCTYPE html>
<html lang="vi">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Qu<PERSON>n lý t<PERSON> | NGUYEN VAN DUY</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@picocss/pico@1/css/pico.min.css">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <style>
    :root {
      --primary-blue: #3b82f6;
      --primary-red: #ef4444;
      --light-blue: #eff6ff;
      --light-red: #fef2f2;
      --dark-blue: #1d4ed8;
      --dark-red: #dc2626;
      --white: #ffffff;
      --gray-50: #f8fafc;
      --gray-100: #f1f5f9;
      --gray-200: #e2e8f0;
      --gray-300: #cbd5e1;
      --gray-400: #94a3b8;
      --gray-500: #64748b;
      --gray-600: #475569;
      --gray-700: #334155;
      --gray-800: #1e293b;
      --gray-900: #0f172a;
      --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
      --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
      --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
      --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
      --border-radius: 8px;
      --border-radius-lg: 12px;
    }

    * {
      box-sizing: border-box;
    }

    body {
      background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      margin: 0;
      min-height: 100vh;
      color: var(--gray-900);
      line-height: 1.6;
    }

    /* Sidebar Styles */
    aside {
      min-width: 260px;
      background: var(--white);
      min-height: 100vh;
      padding: 0;
      position: relative;
      box-shadow: var(--shadow-lg);
      border-right: 1px solid var(--gray-200);
    }

    .sidebar-header {
      padding: 1.5rem 1.25rem;
      border-bottom: 1px solid var(--gray-200);
      background: var(--gray-50);
    }

    .sidebar-logo {
      font-size: 1.5rem;
      font-weight: 800;
      color: var(--primary-blue);
      text-align: center;
      letter-spacing: 1px;
    }

    .sidebar-subtitle {
      text-align: center;
      color: var(--gray-500);
      font-size: 0.8rem;
      margin-top: 0.5rem;
      font-weight: 500;
    }

    nav.sidebar-nav {
      padding: 1.5rem 0;
    }

    nav.sidebar-nav ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    nav.sidebar-nav li {
      margin-bottom: 0.5rem;
      padding: 0 1rem;
    }

    nav.sidebar-nav a {
      color: var(--gray-600);
      text-decoration: none;
      font-size: 0.9rem;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 0.875rem;
      padding: 0.875rem 1rem;
      transition: all 0.2s ease;
      border-radius: var(--border-radius);
      position: relative;
    }

    nav.sidebar-nav a:hover {
      background: var(--gray-100);
      color: var(--gray-900);
      transform: translateX(2px);
    }

    nav.sidebar-nav a.active {
      background: var(--primary-blue);
      color: var(--white);
      font-weight: 600;
      box-shadow: var(--shadow-md);
    }

    nav.sidebar-nav a.active::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 20px;
      background: var(--white);
      border-radius: 0 2px 2px 0;
    }

    nav.sidebar-nav i {
      width: 18px;
      text-align: center;
      font-size: 1rem;
    }

    .sidebar-footer {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 1.25rem;
      border-top: 1px solid var(--gray-200);
      background: var(--gray-50);
    }

    /* Main Content Styles */
    .main-wrapper {
      flex: 1;
      padding: 2rem;
      overflow-y: auto;
      max-width: 1200px;
      background: var(--gray-50);
    }

    .page-header {
      background: var(--white);
      border-radius: var(--border-radius-lg);
      padding: 1.5rem 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--gray-200);
    }

    .breadcrumb {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 1rem;
      font-size: 0.875rem;
      color: var(--gray-500);
    }

    .breadcrumb a {
      color: var(--primary-blue);
      text-decoration: none;
      font-weight: 500;
    }

    .breadcrumb a:hover {
      color: var(--dark-blue);
    }

    .page-title {
      font-size: 1.75rem;
      font-weight: 700;
      color: var(--gray-900);
      margin: 0;
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .page-title i {
      color: var(--primary-blue);
      font-size: 1.5rem;
    }

    /* Profile Section */
    .profile-section {
      background: var(--white);
      border-radius: var(--border-radius-lg);
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--gray-200);
    }

    .profile-header {
      display: flex;
      align-items: center;
      gap: 2rem;
      margin-bottom: 0;
    }

    .avatar-container {
      position: relative;
      flex-shrink: 0;
    }

    .avatar {
      border-radius: 50%;
      width: 80px;
      height: 80px;
      object-fit: cover;
      border: 4px solid var(--primary-blue);
      box-shadow: var(--shadow-md);
    }

    .avatar-badge {
      position: absolute;
      bottom: 2px;
      right: 2px;
      background: #10b981;
      color: var(--white);
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.75rem;
      border: 3px solid var(--white);
      box-shadow: var(--shadow-sm);
    }

    .profile-info h2 {
      font-size: 1.75rem;
      font-weight: 700;
      color: var(--gray-900);
      margin: 0 0 0.5rem 0;
    }

    .profile-info .account-id {
      font-size: 1rem;
      color: var(--gray-500);
      font-weight: 600;
      margin-bottom: 1rem;
      font-family: 'Courier New', monospace;
      background: var(--gray-100);
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      display: inline-block;
    }

    .status-badges {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .status-badge {
      padding: 0.5rem 1rem;
      border-radius: 25px;
      font-weight: 600;
      font-size: 0.8rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      box-shadow: var(--shadow-sm);
    }

    .status-badge.active {
      background: #dcfce7;
      color: #166534;
      border: 1px solid #22c55e;
    }

    .status-badge.verified {
      background: #fef3c7;
      color: #92400e;
      border: 1px solid #f59e0b;
    }

    /* Form Styles */
    .form-section {
      background: var(--white);
      border-radius: var(--border-radius-lg);
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--gray-200);
    }

    .form-section h3 {
      font-size: 1.25rem;
      font-weight: 700;
      color: var(--gray-900);
      margin: 0 0 1.5rem 0;
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid var(--gray-200);
    }

    .form-section h3 i {
      color: var(--primary-blue);
      font-size: 1.125rem;
    }

    .form-grid {
      display: grid;
      gap: 1.5rem;
      grid-template-columns: 1fr;
    }

    @media (min-width: 768px) {
      .form-grid {
        grid-template-columns: 1fr 1fr;
      }
      .form-grid .full-width {
        grid-column: 1 / -1;
      }
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      font-weight: 600;
      color: var(--gray-700);
      margin-bottom: 0.5rem;
      font-size: 0.9rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .form-group label .required {
      color: var(--primary-red);
      font-weight: 700;
    }

    .form-group label i {
      font-size: 0.875rem;
      color: var(--primary-blue);
    }

    .form-group input,
    .form-group select {
      padding: 0.875rem 1rem;
      border: 2px solid var(--gray-200);
      border-radius: var(--border-radius);
      font-size: 0.9rem;
      transition: all 0.2s ease;
      background: var(--white);
      font-weight: 500;
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: var(--primary-blue);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-group input[readonly] {
      background: var(--gray-100);
      color: var(--gray-600);
      cursor: not-allowed;
      border-color: var(--gray-300);
    }

    /* Meta Info */
    .meta-section {
      background: var(--gray-50);
      border-radius: var(--border-radius);
      padding: 1.5rem;
      margin-bottom: 2rem;
      border: 1px solid var(--gray-200);
    }

    .meta-grid {
      display: grid;
      gap: 1.25rem;
      grid-template-columns: 1fr;
    }

    @media (min-width: 768px) {
      .meta-grid {
        grid-template-columns: 1fr 1fr;
      }
    }

    .meta-item {
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
      padding: 1rem;
      background: var(--white);
      border-radius: var(--border-radius);
      border: 1px solid var(--gray-200);
    }

    .meta-label {
      font-size: 0.8rem;
      color: var(--gray-500);
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .meta-value {
      font-size: 0.95rem;
      color: var(--gray-900);
      font-weight: 700;
    }

    /* Action Buttons */
    .action-section {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      flex-wrap: wrap;
      padding-top: 1rem;
      border-top: 1px solid var(--gray-200);
    }

    .btn {
      padding: 0.875rem 1.5rem;
      border-radius: var(--border-radius);
      font-size: 0.9rem;
      font-weight: 600;
      border: 2px solid transparent;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
      text-decoration: none;
      min-width: 120px;
      justify-content: center;
    }

    .btn-primary {
      background: var(--primary-blue);
      color: var(--white);
      box-shadow: var(--shadow-md);
    }

    .btn-primary:hover {
      background: var(--dark-blue);
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }

    .btn-danger {
      background: var(--primary-red);
      color: var(--white);
      box-shadow: var(--shadow-md);
    }

    .btn-danger:hover {
      background: var(--dark-red);
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }

    .btn-secondary {
      background: var(--white);
      color: var(--gray-700);
      border-color: var(--gray-300);
    }

    .btn-secondary:hover {
      background: var(--gray-50);
      border-color: var(--gray-400);
      transform: translateY(-1px);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      aside {
        min-width: 60px;
        padding: 0;
      }

      .sidebar-header {
        padding: 0.75rem 0.5rem;
      }

      .sidebar-logo {
        font-size: 1rem;
        letter-spacing: 1px;
      }

      .sidebar-subtitle {
        display: none;
      }

      nav.sidebar-nav a {
        padding: 0.75rem 0.5rem;
        justify-content: center;
      }

      nav.sidebar-nav a span {
        display: none;
      }

      .main-wrapper {
        padding: 1rem;
      }

      .page-header {
        padding: 1rem;
      }

      .page-title {
        font-size: 1.25rem;
      }

      .profile-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
      }

      .form-section {
        padding: 1rem;
      }

      .action-section {
        justify-content: stretch;
      }

      .btn {
        flex: 1;
        min-width: auto;
      }
    }

    /* Loading Animation */
    .loading {
      opacity: 0.6;
      pointer-events: none;
    }

    .loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 20px;
      height: 20px;
      margin: -10px 0 0 -10px;
      border: 2px solid var(--primary-blue);
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      to {
        transform: rotate(360deg);
      }
    }
  </style>
</head>
<body>
  <div style="display: flex; min-height: 100vh;">
    <!-- Sidebar -->
    <aside>
      <div class="sidebar-header">
        <div class="sidebar-logo">QTTK</div>
        <div class="sidebar-subtitle">Quản Trị Hệ Thống</div>
      </div>

      <nav class="sidebar-nav">
        <ul>
          <li><a href="#"><i class="fas fa-clipboard-list"></i> <span>Quản lý log</span></a></li>
          <li><a href="#" class="active"><i class="fas fa-user-cog"></i> <span>Quản lý tài khoản</span></a></li>
          <li><a href="#"><i class="fas fa-comments"></i> <span>Quản lý tin nhắn</span></a></li>
          <li><a href="#"><i class="fas fa-users"></i> <span>Quản lý khách hàng</span></a></li>
          <li><a href="#"><i class="fas fa-chart-pie"></i> <span>Dashboard</span></a></li>
          <li><a href="#"><i class="fas fa-chart-line"></i> <span>Báo cáo thống kê</span></a></li>
          <li><a href="#"><i class="fas fa-cog"></i> <span>Cài đặt</span></a></li>
        </ul>
      </nav>

      <div class="sidebar-footer">
        <a href="#" style="color: rgba(255, 255, 255, 0.9); text-decoration: none; display: flex; align-items: center; gap: 1rem; padding: 0.5rem;">
          <i class="fas fa-sign-out-alt"></i>
          <span>Đăng xuất</span>
        </a>
      </div>
    </aside>

    <!-- Main Content -->
    <div class="main-wrapper">
      <!-- Page Header -->
      <div class="page-header">
        <div class="breadcrumb">
          <a href="#"><i class="fas fa-home"></i> Trang chủ</a>
          <i class="fas fa-chevron-right"></i>
          <span>Quản lý tài khoản</span>
        </div>
        <h1 class="page-title">
          <i class="fas fa-user-edit"></i>
          Quản lý tài khoản
        </h1>
      </div>

      <!-- Profile Section -->
      <div class="profile-section">
        <div class="profile-header">
          <div class="avatar-container">
            <img class="avatar" src="https://images.unsplash.com/photo-*************-bcfd4ca60f91?auto=format&fit=facearea&w=256&h=256&facepad=2" alt="Avatar Người dùng" />
            <div class="avatar-badge">
              <i class="fas fa-check"></i>
            </div>
          </div>
          <div class="profile-info">
            <h2>NGUYEN VAN DUY</h2>
            <div class="account-id">#********</div>
            <div class="status-badges">
              <span class="status-badge active">
                <i class="fas fa-check-circle"></i>
                Tài khoản hiệu lực
              </span>
              <span class="status-badge verified">
                <i class="fas fa-shield-alt"></i>
                Đã xác thực
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Section -->
      <div class="form-section">
        <h3><i class="fas fa-edit"></i> Thông tin tài khoản</h3>
        <form>
          <div class="form-grid">
            <div class="form-group">
              <label for="username">
                <i class="fas fa-user"></i>
                Tên đăng nhập <span class="required">*</span>
              </label>
              <input type="text" id="username" name="username" value="********" readonly>
            </div>
            <div class="form-group">
              <label for="fullname">
                <i class="fas fa-id-card"></i>
                Họ và tên <span class="required">*</span>
              </label>
              <input type="text" id="fullname" name="fullname" value="NGUYEN VAN DUY" required>
            </div>
            <div class="form-group">
              <label for="email">
                <i class="fas fa-envelope"></i>
                Email <span class="required">*</span>
              </label>
              <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            <div class="form-group">
              <label for="unit">
                <i class="fas fa-building"></i>
                Đơn vị quản lý <span class="required">*</span>
              </label>
              <select id="unit" name="unit" required>
                <option selected>HA DONG T/O</option>
                <option>Đơn vị khác</option>
              </select>
            </div>
            <div class="form-group full-width">
              <label for="user-action">
                <i class="fas fa-tasks"></i>
                Yêu cầu người dùng khi đăng nhập
              </label>
              <select id="user-action" name="user-action">
                <option>Chọn hành động của người dùng khi đăng nhập</option>
                <option>Không yêu cầu</option>
                <option>Yêu cầu đổi mật khẩu</option>
                <option>Yêu cầu xác thực OTP</option>
              </select>
            </div>
          </div>
        </form>
      </div>

      <!-- Meta Information -->
      <div class="meta-section">
        <div class="meta-grid">
          <div class="meta-item">
            <div class="meta-label">Người tạo</div>
            <div class="meta-value">NGUYEN NGOC BAO TRAM</div>
          </div>
          <div class="meta-item">
            <div class="meta-label">Ngày tạo</div>
            <div class="meta-value">15/08/2024 15:39:00</div>
          </div>
          <div class="meta-item">
            <div class="meta-label">Người cập nhật gần nhất</div>
            <div class="meta-value">NGUYEN NGOC BAO TRAM</div>
          </div>
          <div class="meta-item">
            <div class="meta-label">Ngày cập nhật gần nhất</div>
            <div class="meta-value">15/08/2024 15:39:00</div>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="action-section">
        <button type="button" class="btn btn-primary">
          <i class="fas fa-save"></i>
          Lưu thay đổi
        </button>
        <button type="button" class="btn btn-danger">
          <i class="fas fa-trash"></i>
          Xóa tài khoản
        </button>
        <button type="button" class="btn btn-secondary">
          <i class="fas fa-times"></i>
          Hủy bỏ
        </button>
      </div>
    </div>
  </div>

  <script>
    // Add some interactive functionality
    document.addEventListener('DOMContentLoaded', function() {
      // Form validation
      const form = document.querySelector('form');
      const inputs = form.querySelectorAll('input[required], select[required]');

      inputs.forEach(input => {
        input.addEventListener('blur', function() {
          if (!this.value.trim()) {
            this.style.borderColor = 'var(--primary-red)';
          } else {
            this.style.borderColor = 'var(--gray-200)';
          }
        });
      });

      // Button loading states
      const buttons = document.querySelectorAll('.btn');
      buttons.forEach(button => {
        button.addEventListener('click', function() {
          if (!this.classList.contains('btn-secondary')) {
            this.classList.add('loading');
            setTimeout(() => {
              this.classList.remove('loading');
            }, 2000);
          }
        });
      });

      // Sidebar navigation
      const navLinks = document.querySelectorAll('nav.sidebar-nav a');
      navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
          e.preventDefault();
          navLinks.forEach(l => l.classList.remove('active'));
          this.classList.add('active');
        });
      });
    });
  </script>
</body>
</html>
